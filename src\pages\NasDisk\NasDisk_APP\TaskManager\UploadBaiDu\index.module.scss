// 从 DownloadTask 复制样式，用于上传百度网盘组件
.unifiedContainer {
  width: 100%;
  padding: 0;
}

.sectionContainer {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  
  .selectAllCheckbox {
    :global {
      .adm-checkbox {
        --icon-size: 18px;
      }
    }
  }
  
  .clearText {
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.taskItem {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  :global {
    .adm-list-item-content {
      padding: 0;
    }
  }
}

.taskAll {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  width: 100%;
  gap: 12px;
}

.folderIcon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.taskMiddle {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.taskName {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progressBar {
  :global {
    .adm-progress-bar-track {
      height: 8px;
      border-radius: 2px;
    }
    
    .adm-progress-bar-fill {
      height: 8px;
      border-radius: 2px;
    }
  }
}

.bottomRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.fileSize {
  color: #999;
}

.statusText {
  color: #1890ff;
  font-weight: 500;
}

.taskRight {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.pauseButton, .playButton, .retryButton {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &:active {
    background-color: #e6f7ff;
  }
}

.storageLocation {
  font-size: 12px;
  color: #999;
  text-align: right;
}

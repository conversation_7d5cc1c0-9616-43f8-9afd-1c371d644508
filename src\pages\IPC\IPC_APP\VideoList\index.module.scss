.container {
  height: calc(100vh - 35px);
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  padding: 0 8px;
}

.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.selectorContainer {
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 16px;
}

.selector {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px;
  background-color: var(--card-active-background-color);
  border-radius: 18px;

  .selectorText {
    font-size: 12px;
    color: var(--text-color);
    margin-right: 8px;
    margin-left: 8px;
    font-family: MiSans W;
    font-weight: 600;
    font-style: Demibold;
  }

  .selectorArrow {
    width: 24px;
    height: 24px;
  }
}

.content {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
  padding-bottom: 24px;
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.videoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.thumbnailContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  margin-bottom: 8px;
}

.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  //   background: linear-gradient(135deg, #d0d0d0 0%, #e8e8e8 50%, #d0d0d0 100%);
  display: flex;
  align-items: end;
  justify-content: start;
  position: relative;

  &.hidden {
    display: none;
  }
}

.playIcon {
  opacity: 0.6;
  transition: opacity 0.2s ease;
  margin-left: 5px;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    opacity: 0.8;
  }
}

.timeLabel {
  font-size: 14px;
  color: var(--text-color);
  text-align: center;
  font-weight: 400;
}

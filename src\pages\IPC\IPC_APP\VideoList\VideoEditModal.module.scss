.modalContent {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  padding: 10px 0;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.leftIcon,
.rightIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 20px;
    height: 20px;
  }
}

.selectedIcon {
  opacity: 0.6;
  filter: brightness(0.8);
}

.title {
  font-size: 28px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 14px;
}

.content {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
  padding-bottom: 100px; // 为底部操作栏留出空间
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.videoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.thumbnailContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  margin-bottom: 8px;
}

.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: start;
  position: relative;
}

.playIcon {
  opacity: 0.6;
  transition: opacity 0.2s ease;
  margin-left: 5px;

  svg {
    width: 20px;
    height: 20px;
  }
}

.checkboxContainer {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &.checked {
    background-color: #007AFF;
    border-color: #007AFF;
  }
}

.timeLabel {
  font-size: 12px;
  color: var(--text-color);
  text-align: center;
  font-weight: 400;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--background-color);
  border-top: 1px solid var(--border-color);
  padding: 16px;
  display: flex;
  gap: 12px;
  z-index: 10;
}

.actionButton {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  img {
    width: 20px;
    height: 20px;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:not(.disabled):active {
    transform: scale(0.95);
  }
}

.saveButton {
  background-color: #007AFF;
  color: white;
  
  &:not(.disabled):hover {
    background-color: #0056CC;
  }
}

.deleteButton {
  background-color: #FF3B30;
  color: white;
  
  &:not(.disabled):hover {
    background-color: #CC2E24;
  }
}

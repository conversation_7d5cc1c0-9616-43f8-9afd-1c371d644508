import React, { useState, useMemo } from "react";
import styles from "./VideoEditModal.module.scss";
import { PreloadImage } from "@/components/Image";
import closeIcon from "@/Resources/camMgmtImg/close.png";
import closeDarkIcon from "@/Resources/camMgmtImg/close-dark.png";
import saveIcon from "@/Resources/camMgmtImg/finish.png";
import deleteIcon from "@/Resources/camMgmtImg/delete.png";
import outlineIcon from "@/Resources/camMgmtImg/outline.png";
import outlineDarkIcon from "@/Resources/camMgmtImg/outline-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { Divider } from 'antd-mobile'

// 视频项接口
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string;
  media_duration: number;
  file: string;
  create_time: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  timeLabel?: string;
  thumbnail?: string;
}

interface VideoEditModalProps {
  visible: boolean;
  onClose: () => void;
  videoData: VideoItem[];
  videoThumbnails: Map<string, string>;
  onSaveToLocal: (selectedVideos: VideoItem[]) => void;
  onDelete: (selectedVideos: VideoItem[]) => void;
  generateTimeRangeLabel: (startTimestamp: string, durationSeconds: number) => string;
}

export default function VideoEditModal({
  visible,
  onClose,
  videoData,
  videoThumbnails,
  onSaveToLocal,
  onDelete,
  generateTimeRangeLabel
}: VideoEditModalProps) {
  const { isDarkMode } = useTheme();
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());

  // 重置选择状态
  const resetSelection = () => {
    setSelectedVideos(new Set());
  };

  // 关闭模态框
  const handleClose = () => {
    resetSelection();
    onClose();
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedVideos.size === videoData.length) {
      // 当前是全选状态，取消全选
      setSelectedVideos(new Set());
    } else {
      // 全选
      const allVideoKeys = videoData.map(video => `${video.camera_lens}-${video.time}`);
      setSelectedVideos(new Set(allVideoKeys));
    }
  };

  // 切换单个视频选择状态
  const toggleVideoSelection = (video: VideoItem) => {
    const videoKey = `${video.camera_lens}-${video.time}`;
    const newSelected = new Set(selectedVideos);
    
    if (newSelected.has(videoKey)) {
      newSelected.delete(videoKey);
    } else {
      newSelected.add(videoKey);
    }
    
    setSelectedVideos(newSelected);
  };

  // 获取选中的视频列表
  const getSelectedVideoList = (): VideoItem[] => {
    return videoData.filter(video => {
      const videoKey = `${video.camera_lens}-${video.time}`;
      return selectedVideos.has(videoKey);
    });
  };

  // 保存到本机
  const handleSaveToLocal = () => {
    const selectedVideoList = getSelectedVideoList();
    onSaveToLocal(selectedVideoList);
    handleClose();
  };

  // 删除
  const handleDelete = () => {
    const selectedVideoList = getSelectedVideoList();
    onDelete(selectedVideoList);
    handleClose();
  };

  // 标题文本
  const titleText = useMemo(() => {
    if (selectedVideos.size === 0) {
      return "请选择";
    }
    return `已选择${selectedVideos.size}项`;
  }, [selectedVideos.size]);

  // 是否全选状态
  const isAllSelected = selectedVideos.size === videoData.length && videoData.length > 0;

  // 渲染视频项
  const renderVideoItem = (video: VideoItem, index: number) => {
    const videoKey = `${video.camera_lens}-${video.time}`;
    const thumbnailUrl = videoThumbnails.get(videoKey);
    const isSelected = selectedVideos.has(videoKey);

    return (
      <div 
        key={`${video.camera_lens}-${video.time}-${index}`} 
        className={styles.videoItem}
        onClick={() => toggleVideoSelection(video)}
      >
        <div className={styles.thumbnailContainer}>
          <div className={styles.thumbnail}>
            {thumbnailUrl ? (
              // 显示真实的缩略图
              <img
                src={thumbnailUrl}
                alt="视频缩略图"
                className={styles.thumbnailImage}
                onError={(e) => {
                  // 如果图片加载失败，显示占位图
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove(styles.hidden);
                }}
              />
            ) : null}
            {/* 占位图或加载状态 */}
            <div className={`${styles.thumbnailPlaceholder} ${thumbnailUrl ? styles.hidden : ''}`}>
            </div>
            
            {/* 选择框 */}
            <div className={styles.checkboxContainer}>
              <div className={`${styles.checkbox} ${isSelected ? styles.checked : ''}`}>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.timeLabel}>
          {generateTimeRangeLabel(video.time, video.media_duration) || video.timeLabel}
        </div>
      </div>
    );
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={styles.modalContent}>
      {/* 头部 */}
      <div className={styles.header}>
        <div className={styles.leftIcon} onClick={handleClose}>
          <PreloadImage src={isDarkMode ? closeDarkIcon : closeIcon} />
        </div>
        <div className={styles.rightIcon} onClick={handleSelectAll}>
          <PreloadImage
            src={isDarkMode ? outlineDarkIcon : outlineIcon}
            className={isAllSelected ? styles.selectedIcon : ''}
          />
        </div>
      </div>
      <div className={styles.title}>{titleText}</div>

      {/* 视频网格 */}
      <div className={styles.content}>
        <Divider />
        <div className={styles.videoGrid}>
          {videoData.map((video, index) => renderVideoItem(video, index))}
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className={styles.footer}>
        <button
          className={`${styles.actionButton} ${styles.saveButton} ${selectedVideos.size === 0 ? styles.disabled : ''}`}
          onClick={handleSaveToLocal}
          disabled={selectedVideos.size === 0}
        >
          <PreloadImage src={saveIcon} />
          <span>保存到本机</span>
        </button>
        <button
          className={`${styles.actionButton} ${styles.deleteButton} ${selectedVideos.size === 0 ? styles.disabled : ''}`}
          onClick={handleDelete}
          disabled={selectedVideos.size === 0}
        >
          <PreloadImage src={deleteIcon} />
          <span>删除</span>
        </button>
      </div>
    </div>
  );
}
